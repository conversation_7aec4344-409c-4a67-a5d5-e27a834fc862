<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Backend API Test</h1>
    
    <div class="test-section">
        <h3>Health Check</h3>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Login Test</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Books Test</h3>
        <button onclick="testBooks()">Test Books Endpoint</button>
        <div id="books-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>Success:</strong> ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `<strong>Login Success:</strong> ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>Login Failed:</strong> ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        async function testBooks() {
            const resultDiv = document.getElementById('books-result');
            try {
                const response = await fetch(`${API_BASE}/api/books`);
                const data = await response.json();
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `<strong>Success:</strong> ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>Error:</strong> ${error.message}`;
            }
        }
        
        // Auto-test health on load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
